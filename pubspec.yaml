name: up_school_parent
description: UpSchool Parent App.

publish_to: 'none'


version: 1.10.21+121

environment:
  sdk: '>=3.0.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # * Helper Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #! Webview
  flutter_inappwebview: ^6.1.0+1
#  webview_flutter: ^4.7.0
  flutter_downloader: ^1.11.7
  android_path_provider: ^0.3.1
#  open_file:
  open_file_safe_plus: ^0.0.6
  share_plus:
  permission_handler: ^11.3.1
  external_path: ^2.2.0
#  open_filex: ^4.7.0

  #! Firebase
#  firebase_core: ^2.24.2
#  firebase_messaging: ^14.7.10

  #! Restart App
  restart_app: ^1.2.1

  #! State Management
  provider: ^6.1.2
  riverpod:

  #! Animated
  lottie: ^3.0.0

  #! Check Connectivity
  internet_connection_checker: ^1.0.0+1

  #! Notifications
  onesignal_flutter: ^5.3.4
#  onesignal_flutter: ^5.0.4

  http:
  path_provider:
  device_info_plus: ^10.1.0

  dio:

  url_launcher: ^6.3.0
#  ext_storage:

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4
  url_launcher: ^6.3.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:

  uses-material-design: true

  assets:
    - assets/images/
    - assets/animated/
    - shorebird.yaml