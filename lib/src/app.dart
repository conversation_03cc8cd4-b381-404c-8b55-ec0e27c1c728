import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:up_school_parent/src/core/config/app_config.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';
import 'package:up_school_parent/src/pages/web_view_page.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (BuildContext context) => AppConfig()..init(),
      child: MaterialApp(
        title: AppConstants.appName,
        theme: ThemeData(
          primarySwatch: Colors.blueGrey,
        ),
        debugShowCheckedModeBanner: false,
        debugShowMaterialGrid: false,
        home: const WebViewPage(),
      ),
    );
  }
}
