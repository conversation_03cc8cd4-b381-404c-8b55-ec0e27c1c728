import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';

class OneSignalNotificationService {
  static Future<void> initOneSignal() async {
    OneSignal.Debug.setLogLevel(
      kDebugMode ? OSLogLevel.verbose : OSLogLevel.none,
    );

    OneSignal.initialize(AppConstants.oneSignalAppId);

    //? check if has permission
    OneSignal.Debug.setLogLevel(
      kDebugMode ? OSLogLevel.verbose : OSLogLevel.none,
    );

    OneSignal.initialize(AppConstants.oneSignalAppId);

    //? check if has permission
    final canRequestPermission = await OneSignal.Notifications.canRequest();

    if (canRequestPermission) {
      await OneSignal.Notifications.requestPermission(true);
    }
  }

  //? Get User Id
  static String getUserId() {
    final id = OneSignal.User.pushSubscription.id;
    return id ?? '';
  }
}
