import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:open_file_safe_plus/open_file_safe_plus.dart' as file_opener;
import 'package:riverpod/riverpod.dart';
import 'package:up_school_parent/src/pages/services/download_methods.dart';
import 'package:xr_helper/xr_helper.dart';

enum ExportType { pdf, xlsx, print }

final reportSettingRemoteRepositoryProvider =
    Provider<ReportSettingRemoteRepository>(
  (ref) {
    return DownloadRepository();
  },
);

abstract class ReportSettingRemoteRepository {
  Future<dynamic> open(
      {required String title,
      ExportType exportType = ExportType.pdf,
      required Map body,
      required String endPointUrl,
      bool isShare = false});
}

class DownloadRepository extends ReportSettingRemoteRepository {
  final BaseApiServices apiService = NetworkApiServices();

  Future<dynamic> _getBlobData(String endPoint, {body}) async {
    log('ReportApi: $endPoint');

    try {
      var response;

      if (body == null) {
        response = await apiService.getResponse(
          endPoint,
          blobData: true,
        );

        return response as Uint8List;
      } else {
        response = await apiService.postResponse(
          endPoint,
          blobData: true,
          body: body,
        );
        final decodedBase64 = base64.decode(response);

        Log.w('RESPONSEEE $decodedBase64');

        final Uint8List uint8List = Uint8List.fromList(decodedBase64);

        return uint8List;
      }
    } catch (error) {
      log('ClearanceReportSettingRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<dynamic> open(
      {required String title,
      ExportType exportType = ExportType.pdf,
      required String endPointUrl,
      Map? body,
      bool isShare = false}) async {
    final endPoint = endPointUrl;

    try {
      final Uint8List pdfBlob = await _getBlobData(endPoint, body: body);

      final path = await findLocalPath();

      final bufferList = pdfBlob.buffer.asUint8List();

      final sanitizedTitle = sanitizeFilename(title);

      final normalFileExtension = endPoint.split('.').lastOrNull;

      final pdfFile =
          File("$path/$sanitizedTitle.${normalFileExtension ?? 'pdf'}");

      await pdfFile.writeAsBytes(bufferList);

      Log.w('PDFFILEPATH: ${pdfFile.path}');

      await file_opener.OpenFileSafePlus.open(pdfFile.path);
    } catch (error) {
      log('ERROR: $error');

      rethrow;
    }
  }
}

String sanitizeFilename(String filename) {
  return filename.replaceAll(RegExp(r'[\/:*?"<>|]'), '_');
}

void createDirectory(String path) {
  final directory = Directory(path);
  if (!directory.existsSync()) {
    directory.createSync(recursive: true);
  }
}

// Future<String?> findLocalPath() async {
//   var appSpecificDirPath;
//
//   if (Platform.isAndroid) {
//     try {
//       appSpecificDirPath = await ExternalPath.getExternalStoragePublicDirectory(
//           ExternalPath.DIRECTORY_DOWNLOADS);
//     } catch (e) {
//       final directory = await getExternalStorageDirectory();
//       appSpecificDirPath = directory?.path;
//     }
//   } else if (Platform.isIOS) {
//     appSpecificDirPath =
//         (await getApplicationDocumentsDirectory()).absolute.path;
//   }
//
//   return appSpecificDirPath;
// }
// Future<String?> findLocalPath() async {
//   final deviceInfo = DeviceInfoPlugin();
//   if (Platform.isAndroid &&
//       (await deviceInfo.androidInfo).version.sdkInt >= 29) {
//     // Use scoped storage.
//     final directory = await getApplicationDocumentsDirectory();
//     return directory.path;
//   } else {
//     // Request for write external storage permission.
//     if (await Permission.storage.request().isGranted) {
//       final directory = await getExternalStorageDirectory();
//       return directory?.path;
//     }
//   }
//   return null;
// }

// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'dart:typed_data';
//
// import 'package:external_path/external_path.dart';
// import 'package:open_file/open_file.dart' as file_opener;
// import 'package:path_provider/path_provider.dart';
// import 'package:riverpod/riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// enum ExportType { pdf, xlsx, print }
//
// final reportSettingRemoteRepositoryProvider =
//     Provider<ReportSettingRemoteRepository>(
//   (ref) {
//     return DownloadRepository();
//   },
// );
//
// abstract class ReportSettingRemoteRepository {
//   Future<dynamic> open(
//       {required String title,
//       ExportType exportType = ExportType.pdf,
//       required Map body,
//       required String endPointUrl,
//       bool isShare = false});
// }
//
// class DownloadRepository extends ReportSettingRemoteRepository {
//   final BaseApiServices apiService = NetworkApiServices();
//
//   Future<dynamic> _getBlobData(String endPoint, {body}) async {
//     log('ReportApi: $endPoint');
//
//     try {
//       var response;
//
//       if (body == null) {
//         response = await apiService.getResponse(
//           endPoint,
//           blobData: true,
//         );
//
//         return response as Uint8List;
//       } else {
//         response = await apiService.postResponse(
//           endPoint,
//           blobData: true,
//           body: body,
//         );
//         final decodedBase64 = base64.decode(response);
//
//         Log.w('RESPONSEEE $decodedBase64');
//
//         final Uint8List uint8List = Uint8List.fromList(decodedBase64);
//
//         return uint8List;
//       }
//     } catch (error) {
//       log('ClearanceReportSettingRemoteDataSourceImplError: $error');
//       rethrow;
//     }
//   }
//
//   @override
//   Future<dynamic> open(
//       {required String title,
//       ExportType exportType = ExportType.pdf,
//       required String endPointUrl,
//       Map? body,
//       bool isShare = false}) async {
//     final endPoint = endPointUrl;
//
//     log('ReportApi: $endPoint');
//
//     try {
//       final Uint8List pdfBlob = await _getBlobData(endPoint, body: body);
//
//       final path = await findLocalPath();
//
//       final bufferList = pdfBlob.buffer.asUint8List();
//
//       // final pdfFile = File("${path?.path}/$title.pdf");
//
//       if (body != null) {
//         final pdfFile = File("$path/$title.pdf");
//
//         await pdfFile.writeAsBytes(bufferList);
//
//         await file_opener.OpenFile.open(pdfFile.path);
//       } else {
//         final normalFileExtension = endPoint.split('.').lastOrNull;
//
//
//         final pdfFile = File("$path/$title.${normalFileExtension ?? 'pdf'}");
//
//         await pdfFile.writeAsBytes(bufferList);
//
//         await file_opener.OpenFile.open(pdfFile.path);
//       }
//     } catch (error) {
//       log('ERROR: $error');
//
//       rethrow;
//     }
//   }
// }
//
// Future<String?> findLocalPath() async {
//   var appSpecificDirPath;
//
//   if (Platform.isAndroid) {
//     try {
//       appSpecificDirPath = await ExternalPath.getExternalStoragePublicDirectory(
//           ExternalPath.DIRECTORY_DOWNLOADS);
//     } catch (e) {
//       final directory = await getExternalStorageDirectory();
//       appSpecificDirPath = directory?.path;
//     }
//   } else if (Platform.isIOS) {
//     appSpecificDirPath =
//         (await getApplicationDocumentsDirectory()).absolute.path;
//   }
//
//   return appSpecificDirPath;
// }
//
// // Future<String?> findLocalPath() async {
// //   final deviceInfo = DeviceInfoPlugin();
// //   if (Platform.isAndroid &&
// //       (await deviceInfo.androidInfo).version.sdkInt >= 29) {
// //     // Use scoped storage.
// //     final directory = await getApplicationDocumentsDirectory();
// //     return directory.path;
// //   } else {
// //     // Request for write external storage permission.
// //     if (await Permission.storage.request().isGranted) {
// //       final directory = await getExternalStorageDirectory();
// //       return directory?.path;
// //     }
// //   }
// //   return null;
// // }
