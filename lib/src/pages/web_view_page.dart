import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:up_school_parent/src/core/utils/one_signal_service.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';
import 'package:up_school_parent/src/pages/services/download_methods.dart';
import 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';
import 'package:xr_helper/xr_helper.dart';
import '../core/config/app_config.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {

  // Function to handle external links and open them in the browser
  Future<void> _launchExternalLink(String url) async {
    final Uri uri = Uri.parse(url);  // Convert the string URL to a Uri object
    if (await canLaunchUrl(uri)) { 
      await launchUrl(uri, mode: LaunchMode.externalApplication);  // Open in external browser
    }
  }

  void onDownloadStart(controller, request) async {
    Log.i("onDownloadStartSSSS $request");

    try {
      await prepareSaveDir();

      final lang = await controller.evaluateJavascript(
          source: "window.localStorage.getItem('UPSPlang')");

      final isArabic = lang.toString().contains("ar");

      context.showBarMessage(isArabic ? "...جاري الفتح" : "מתחיל הורדה...");

      downloadFiles(context, controller: controller);
    } catch (e) {
      Log.e('DownloadError: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer<AppConfig>(
          builder: (context, appConfig, child) {
            if (!appConfig.hasInternet) {
              return const NoInternetConnectionWidget();
            }

            final token = OneSignalNotificationService.getUserId();

            return InAppWebView(
              onWebViewCreated: appConfig.onWebViewCreated,
              onLoadStop: (controller, url) async {
                await appConfig.addTokenToLogin(controller: controller);
              },
              onProgressChanged: (controller, progress) async {
                if (progress == 100) {
                  await appConfig.addTokenToLogin(controller: controller);
                }
              },
              onUpdateVisitedHistory: (controller, url, androidIsReload) async {
                await appConfig.addTokenToLogin(controller: controller);
              },
              onDownloadStartRequest: onDownloadStart,
              onReceivedServerTrustAuthRequest: (controller, challenge) async {
                return ServerTrustAuthResponse(
                    action: ServerTrustAuthResponseAction.PROCEED);
              },
              initialUrlRequest: URLRequest(
                url: WebUri.uri(
                  Uri.parse('${AppConstants.appUrl}?token=$token'),
                ),
              ),
              onLoadStart: (controller, url) async {
                // Check if the link is external
                if (url?.toString().startsWith('https://upschool.org.il') == true) {
                  // Launch the external link in the browser
                  _launchExternalLink(url.toString());
                  controller.stopLoading();  // Prevent WebView from opening it
                }
              },
            );
          },
        ),
      ),
    );
  }
}
