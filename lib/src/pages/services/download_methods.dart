import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:android_path_provider/android_path_provider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:open_file_safe_plus/open_file_safe_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:up_school_parent/src/core/config/app_config.dart';
import 'package:xr_helper/xr_helper.dart';

void downloadCallback(id, status, progress) {
  final SendPort send =
      IsolateNameServer.lookupPortByName('downloader_send_port')!;
  send.send([id, status, progress]);
}

late String localPath;

Future<void> prepareSaveDir() async {
  localPath = (await findLocalPath())!;
  final savedDir = Directory(localPath);
  bool hasExisted = await savedDir.exists();
  if (!hasExisted) {
    savedDir.create();
  }
  return;
}

Future<String?> findLocalPath() async {
  var externalStorageDirPath;

  if (Platform.isAndroid) {
    try {
      externalStorageDirPath = await AndroidPathProvider.documentsPath;
    } catch (e) {
      final directory = await getApplicationDocumentsDirectory();
      externalStorageDirPath = directory?.path;
    }
  } else if (Platform.isIOS) {
    externalStorageDirPath =
        (await getApplicationDocumentsDirectory()).absolute.path;
  }

  return externalStorageDirPath;
}

// * Download Files ========================================

void downloadFiles(
  BuildContext context, {
  required InAppWebViewController controller,
}) async {
  final data = await controller.evaluateJavascript(
      source: "window.localStorage.getItem('UPSPlink')");

  if (data == null) return;

  final decodedData = jsonDecode(data);

  final url = decodedData['url'];
  final token = decodedData['token'];

  if (url == null || url.isEmpty) {
    return;
  }

  final fileExtension = url.split('.').last.toLowerCase();

  // Show a dialog while downloading
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return const Center(child: CircularProgressIndicator());
    },
  );

  try {
    // Download the file using Dio
    final tempDir = await getTemporaryDirectory();

    final filePath = '${tempDir.path}/downloaded_file.$fileExtension';

    final response = await Dio().download(
      url,
      filePath,
      options: Options(headers: {'Authorization': 'Bearer $token'}),
    );
    if (response.statusCode == 200) {
      // Close the dialog
      context.back();

      Log.w('File_downloaded successfully at $filePath');

      // Open the file with appropriate viewer
      OpenResult result = await OpenFileSafePlus.open(filePath);

      Log.i('Response: ${response.statusCode} RESSSS ${response.data}');

      if (result.type == ResultType.noAppToOpen) {
        context.showBarMessage('No app to open the file', isError: true);

        await FlutterDownloader.enqueue(
          url: url,
          headers: <String, String>{
            "Authorization": "Bearer $token",
          },
          savedDir: localPath,
          showNotification: true,
          saveInPublicStorage: true,
          openFileFromNotification: true,
        );
      }
    } else {
      context.showBarMessage('Error downloading file', isError: true);
      await FlutterDownloader.enqueue(
        url: url,
        headers: <String, String>{
          "Authorization": "Bearer $token",
        },
        savedDir: localPath,
        showNotification: true,
        saveInPublicStorage: true,
        openFileFromNotification: true,
      );
    }
  } catch (e, s) {
    context.back();
    context.showBarMessage('ErRRR ${e}\n$s', isError: true);
    await FlutterDownloader.enqueue(
      url: url,
      headers: <String, String>{
        "Authorization": "Bearer $token",
      },
      savedDir: localPath,
      showNotification: true,
      saveInPublicStorage: true,
      openFileFromNotification: true,
    );
  }
}

// import 'dart:convert';
// import 'dart:io';
// import 'dart:isolate';
// import 'dart:ui';
//
// import 'package:android_path_provider/android_path_provider.dart';
// import 'package:flutter_downloader/flutter_downloader.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// void downloadCallback(id, status, progress) {
//   final SendPort send =
//   IsolateNameServer.lookupPortByName('downloader_send_port')!;
//   send.send([id, status, progress]);
// }
//
// late String localPath;
//
// Future<void> prepareSaveDir() async {
//   localPath = (await findLocalPath())!;
//   final savedDir = Directory(localPath);
//   bool hasExisted = await savedDir.exists();
//   if (!hasExisted) {
//     savedDir.create();
//   }
//   return;
// }
//
// Future<String?> findLocalPath() async {
//   var externalStorageDirPath;
//
//   if (Platform.isAndroid) {
//     try {
//       externalStorageDirPath = await AndroidPathProvider.documentsPath;
//     } catch (e) {
//       final directory = await getApplicationDocumentsDirectory();
//       externalStorageDirPath = directory?.path;
//     }
//   } else if (Platform.isIOS) {
//     externalStorageDirPath =
//         (await getApplicationDocumentsDirectory()).absolute.path;
//   }
//
//   return externalStorageDirPath;
// }
//
// // * Download Files ========================================
//
// void downloadFiles({
//   required InAppWebViewController controller,
// }) async {
//   final data = await controller.evaluateJavascript(
//       source: "window.localStorage.getItem('UPSPlink')");
//
//   if (data == null) return;
//
//   final decodedData = jsonDecode(data);
//
//   Log.i('DECODED DATA $decodedData');
//
//   await GetStorageService.setLocalData(
//       key: LocalKeys.token, value: decodedData['token']);
//
//   final url = decodedData['url'];
//   final token = decodedData['token'];
//
//   Log.w('TOKEN $token');
//
//   final fileExtension = url.split('.').last;
//
//   Log.f('URLink $url');
//
//   Log.f('File_Extension $fileExtension');
//
//   await GetStorageService.setLocalData(key: LocalKeys.token, value: token);
//
//   if (url == null || url.isEmpty) {
//     return;
//   }
//
//   await FlutterDownloader.enqueue(
//     url: url,
//     headers: <String, String>{
//       "Authorization": "Bearer $token",
//     },
//     savedDir: localPath,
//     showNotification: true,
//     saveInPublicStorage: true,
//     openFileFromNotification: true,
//   );
// }
