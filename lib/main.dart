import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:up_school_parent/src/app.dart';
import 'package:up_school_parent/src/core/utils/one_signal_service.dart';



void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  HttpOverrides.global = MyHttpOverrides();

  final deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid &&
      (await deviceInfo.androidInfo).version.sdkInt > 29) {
    await Permission.manageExternalStorage.request();
  } else {
    await Permission.storage.request();
  }

  await FlutterDownloader.initialize(
      debug:
      kDebugMode,
      ignoreSsl:
          true
      );


  //! Initialize Firebase Messaging
  await OneSignalNotificationService.initOneSignal();

  if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(true);
  }

  runApp(const MyApp());
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (cert, host, port) => true;
  }
}
